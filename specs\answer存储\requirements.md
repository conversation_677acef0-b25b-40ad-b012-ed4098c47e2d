# 需求文档

## 介绍

本需求旨在优化 EchoSync 插件中提示词的存储和共享机制，实现多平台间提示词的高效复用和答案管理。核心思想是让相同的提示词在不同平台上共享同一个 chat_uid，从而在数据库中只存储一份提示词内容，但可以关联多个平台的回答。

## 需求

### 需求 1 - 提示词存储与 chat_uid 生成

**用户故事：** 作为用户，我希望在输入提示词后能够存档，以便后续复用或在其他平台使用。

#### 验收标准

1. When 用户在 content 页面输入提示词并点击存档时，系统应生成 chat_uid 并将提示词存储到数据库。
2. When 用户修改了提示词并再次点击存储时，系统应生成新的 chat_uid。
3. When 用户不点击存档直接发送提示词时，系统应使用已存在的 chat_uid 存入数据库的 `chat_prompt` 和 `chat_history` 表，其中 chat_history 中 is_answer 字段为 0。

### 需求 2 - 答案存储机制

**用户故事：** 作为用户，我希望系统能自动保存 AI 的回答，并与对应的提示词关联。

#### 验收标准

1. When AI 回答完毕时，系统应将答案存入数据库的 `chat_history` 表，其中 is_answer 字段为 1，platform_id 为当前 content 的 platform_id。
2. While 等待 AI 回答或取消发送前，content 页面应一直使用当前的 chat_uid。

### 需求 3 - 跨平台提示词共享

**用户故事：** 作为用户，我希望能在不同 AI 平台间共享和复用相同的提示词，避免重复输入。

#### 验收标准

1. When 用户在其他 content 页面点击提示词历史中的某个提示词时，系统应将该提示词注入当前页面的输入框，并保持原有的 chat_uid。
2. When 用户在复制提示词后点击存档时，如果提示词内容未变，系统应保持 chat_uid 不变，不更新 chat_prompt 表，仅在 chat_history 表插入记录。
3. When 用户手动修改了输入框中的提示词内容时，系统应生成新的 chat_uid，并将其视为新的提示词存入数据库。

### 需求 4 - 数据结构优化

**用户故事：** 作为系统管理员，我希望数据结构能够高效支持提示词共享和多平台回答存储。

#### 验收标准

1. The 系统应确保相同的提示词在 chat_prompt 表中只有一条记录。
2. The 系统应允许一个 prompt（通过 chat_uid 关联）在 chat_history 表中有多条记录，对应不同平台的回答。
3. The 系统应通过 chat_uid 将 chat_prompt 和 chat_history 表关联起来，实现一对多的关系。