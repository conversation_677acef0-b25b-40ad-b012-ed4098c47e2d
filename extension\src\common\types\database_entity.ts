// 数据库实体类型定义

// 基础实体类型
export interface DatabaseRow {
  id: number
  created_at?: number
  updated_at?: number
}

// 平台实体
export interface Platform extends DatabaseRow {
  name: string
  url: string
  icon?: string
  icon_base64?: string
  is_delete: number
}

// 聊天提示词实体
export interface ChatPrompt extends DatabaseRow {
  chat_prompt: string
  chat_uid: string
  create_time: number
  is_synced: number
  is_delete: number
}

// 聊天历史实体
export interface ChatHistory extends DatabaseRow {
  chat_answer?: string
  chat_uid: string
  platform_id: number
  tags?: string
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  create_time: number
  is_synced: number
  is_answered: number
  is_delete: number
}

// 聊天历史与平台的联合实体
export interface ChatHistoryWithPlatform extends ChatHistory {
  platform_name: string
  platform_url: string
  platform_icon?: string
  platform_icon_base64?: string
}

// 聊天提示词与平台统计的联合实体
export interface ChatPromptWithPlatforms extends ChatPrompt {
  platforms: Array<{
    platform_id: number
    platform_name: string
    platform_icon?: string
    platform_icon_base64?: string
    count: number
    latest_time: number
  }>
}

// 数据库配置实体
export interface DatabaseConfig {
  path: string
  timeout: number
  max_connections: number
  enable_wal: boolean
  enable_foreign_keys: boolean
}

// 数据库迁移实体
export interface Migration {
  version: number
  name: string
  up: string[]
  down: string[]
}