# 实施计划

## 阶段1：基础架构搭建

- [ ] 1. 创建事件总线系统
  - 实现 EventBus 类，支持事件订阅、发布、取消订阅
  - 添加类型安全的事件定义
  - 实现事件防抖和错误处理机制
  - _需求: 需求2_

- [ ] 2. 创建元素管理器
  - 实现 ElementManager 类，负责 DOM 元素的查找和缓存
  - 添加元素变化监听机制
  - 实现元素失效检测和重新获取逻辑
  - _需求: 需求1, 需求3_

- [ ] 3. 重构 BaseAIAdapter 核心架构
  - 集成事件总线到 BaseAIAdapter
  - 添加元素管理器到 BaseAIAdapter
  - 实现应用上下文的核心功能
  - _需求: 需求1, 需求6_

## 阶段2：选择器配置系统重构

- [ ] 4. 分离通用选择器配置
  - 从 platformConfigs.ts 中提取通用选择器
  - 创建 CommonSelectors 配置类
  - 移除平台特定的选择器到各自适配器
  - _需求: 需求3_

- [ ] 5. 重构各平台适配器选择器
  - 在 DeepSeekAdapter 中添加平台特定选择器
  - 在 KimiAdapter 中添加平台特定选择器
  - 在 ChatGPTAdapter 中添加平台特定选择器
  - _需求: 需求3_

- [ ] 6. 实现选择器合并逻辑
  - 在各适配器子类中实现 mergeSelectors 方法
  - 添加选择器优先级和去重逻辑
  - 实现选择器验证机制
  - _需求: 需求3_

## 阶段3：组件重构

- [ ] 7. 重构 ArchiveButton 组件
  - 移除 ArchiveButton 中的 DOM 查找逻辑
  - 通过 BaseAIAdapter 实例获取 inputContainer
  - 实现事件订阅机制替代直接事件监听
  - _需求: 需求1, 需求2_

- [ ] 8. 重构 InputManager 组件
  - 让 InputManager 持有 BaseAIAdapter 实例
  - 通过 BaseAIAdapter 获取页面元素
  - 移除 InputManager 中的直接 DOM 操作
  - _需求: 需求5_

- [ ] 9. 重构 FloatingBubble 组件
  - 通过 BaseAIAdapter 获取定位相关元素
  - 使用事件总线监听页面变化
  - 移除直接的 DOM 查找逻辑
  - _需求: 需求1, 需求6_

## 阶段4：数据库交互规范化

- [ ] 10. 审查和重构数据库交互
  - 检查 content 模块中的所有数据库访问代码
  - 确保所有数据库操作通过 MessagingService
  - 重构 ArchiveButton 的数据库交互逻辑
  - _需求: 需求4_

- [ ] 11. 完善 MessagingService 接口
  - 添加类型安全的消息定义
  - 实现错误处理和重试机制
  - 添加消息队列和批处理功能
  - _需求: 需求4_

## 阶段5：集成和优化

- [ ] 12. 更新 content/index.ts 入口文件
  - 重构初始化流程，以 BaseAIAdapter 为核心
  - 实现组件的依赖注入
  - 添加错误处理和降级机制
  - _需求: 需求6_

- [ ] 13. 性能优化和缓存机制
  - 实现元素缓存策略
  - 添加事件防抖和节流
  - 优化内存使用和垃圾回收
  - _需求: 需求1, 需求6_

- [ ] 14. 错误处理和日志系统
  - 添加统一的错误处理机制
  - 实现日志收集和上报
  - 添加性能监控和调试工具
  - _需求: 需求6_

## 阶段6：测试和文档

- [ ] 15. 编写单元测试
  - 为 EventBus 编写测试用例
  - 为 ElementManager 编写测试用例
  - 为各适配器编写测试用例
  - _需求: 所有需求_

- [ ] 16. 集成测试
  - 测试组件间的事件通信
  - 测试选择器合并逻辑
  - 测试数据库交互流程
  - _需求: 所有需求_

- [ ] 17. 更新技术文档
  - 编写新架构的使用指南
  - 更新 API 文档
  - 添加最佳实践和示例代码
  - _需求: 所有需求_
