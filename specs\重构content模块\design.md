# 技术方案设计

## 架构概述

重构content模块，采用标准适配器模式，将职责清晰分离，提高代码的可维护性和扩展性。

## 核心设计原则

### 1. 单一职责原则
- AIAdapter基类专注于页面DOM操作和事件监听
- 消息通信逻辑独立到MessageHandler
- 平台检测逻辑独立到PlatformDetector

### 2. 适配器模式
- 父类提供完整的流程控制和通用实现
- 子类只需实现平台特有的差异化逻辑
- 通过配置驱动的方式减少子类代码量

### 3. 正则表达式驱动
- 使用正则表达式进行灵活的元素匹配
- 父类提供通用的正则表达式模式
- 子类可以覆盖特定的匹配规则

## 技术架构

```mermaid
graph TB
    A[ContentScriptManager] --> B[PlatformDetector]
    A --> C[MessageHandler]
    A --> D[AIAdapter基类]
    
    D --> E[DOMElementCapture]
    D --> F[EventListener]
    D --> G[PageInteraction]
    
    D --> H[ChatGPTAdapter]
    D --> I[ClaudeAdapter]
    D --> J[DeepSeekAdapter]
    D --> K[GeminiAdapter]
    D --> L[KimiAdapter]
    
    H --> M[ChatGPT配置]
    I --> N[Claude配置]
    J --> O[DeepSeek配置]
    K --> P[Gemini配置]
    L --> Q[Kimi配置]
```

## 模块设计

### 1. ContentScriptManager（入口管理器）
- 负责整体生命周期管理
- 协调各个模块的初始化
- 处理SPA路由变化

### 2. PlatformDetector（平台检测器）
- 使用正则表达式检测当前平台
- 返回对应的适配器实例
- 支持动态平台检测

### 3. MessageHandler（消息处理器）
- 处理与background的消息通信
- 将消息路由到对应的适配器方法
- 统一的错误处理和响应格式

### 4. AIAdapter（重构后的基类）
- 专注于页面DOM操作
- 提供通用的元素捕获方法
- 统一的事件监听机制
- 配置驱动的选择器管理

### 5. 平台适配器（子类）
- 只需提供平台特有的配置
- 可选择性重写特定方法
- 最小化代码重复

## 配置驱动设计

### 平台配置结构
```typescript
interface PlatformConfig {
  name: string
  id: AIPlatform
  url: string
  patterns: {
    hostname: RegExp
    validPath?: RegExp
  }
  selectors: {
    inputField: string[]
    sendButton: string[]
    messageContainer: string[]
    userMessage?: string[]
    assistantMessage?: string[]
  }
  regexPatterns?: {
    messageExtraction?: RegExp
    titleExtraction?: RegExp
    contentCleaning?: RegExp
  }
}
```

## 重构策略

### 阶段1：基础架构重构
1. 创建新的基类结构
2. 实现配置驱动机制
3. 重构消息处理逻辑

### 阶段2：适配器重构
1. 将现有适配器转换为配置驱动
2. 简化子类实现
3. 统一正则表达式使用

### 阶段3：优化和测试
1. 性能优化
2. 错误处理完善
3. 兼容性测试

## 技术选型

- **TypeScript**: 类型安全和更好的开发体验
- **正则表达式**: 灵活的元素匹配和内容提取
- **观察者模式**: 事件监听和状态管理
- **工厂模式**: 适配器实例创建

## 预期收益

1. **代码复用**: 子类代码量减少60%以上
2. **维护性**: 统一的架构和配置管理
3. **扩展性**: 新平台接入只需配置文件
4. **稳定性**: 统一的错误处理和日志记录
