# Content模块重构实施计划

## 阶段一：基础架构重构

### 任务 1.1 - 重构事件总线系统
- **具体内容**：
  - 创建EventEnum.ts定义所有事件类型枚举
  - 重构EventBus.ts，增强类型安全性和错误处理
  - 添加事件调试和监控功能
  - 编写事件总线的单元测试
- **预计时间**：4小时
- **验收标准**：事件总线支持类型安全的事件发布订阅，具备完整的错误处理机制
- _需求: 需求2_

### 任务 1.2 - 设计基础类型系统
- **具体内容**：
  - 在types目录下创建完整的类型定义文件
  - 定义事件类型、组件接口、适配器接口等
  - 建立统一的配置类型定义
  - 确保所有类型的导出和引用关系正确
- **预计时间**：3小时
- **验收标准**：所有模块使用统一的类型定义，类型检查无错误
- _需求: 需求8_

### 任务 1.3 - 创建基础抽象类
- **具体内容**：
  - 创建BaseInject抽象类，定义注入器的基本接口
  - 创建BaseComponent抽象类，定义UI组件的基本接口
  - 建立组件生命周期管理机制
  - 添加基础的错误处理和日志记录
- **预计时间**：3小时
- **验收标准**：基础抽象类提供完整的接口定义和生命周期管理
- _需求: 需求3, 需求6_

## 阶段二：页面捕获模块重构

### 任务 2.1 - 实现ContentCapturer
- **具体内容**：
  - 创建ContentCapturer类，负责页面内容捕获
  - 实现对话内容提取逻辑
  - 添加元素查找和内容清理功能
  - 通过事件总线发布捕获结果
- **预计时间**：5小时
- **验收标准**：能够准确捕获各平台的对话内容，并通过事件总线通知
- _需求: 需求4_

### 任务 2.2 - 实现ContentListener
- **具体内容**：
  - 创建ContentListener类，统一管理页面事件监听
  - 实现输入框焦点、内容变化等事件监听
  - 添加页面变化检测机制
  - 通过事件总线发布页面事件
- **预计时间**：4小时
- **验收标准**：能够监听所有关键页面事件，并正确发布到事件总线
- _需求: 需求4_

### 任务 2.3 - 优化元素查找机制
- **具体内容**：
  - 重构DOMUtils，提供更强大的元素查找功能
  - 创建ElementFinder工具类，支持多种查找策略
  - 添加元素缓存和性能优化
  - 支持动态页面的元素查找
- **预计时间**：4小时
- **验收标准**：元素查找准确率高，性能优良，支持SPA页面
- _需求: 需求4_

## 阶段三：UI组件与注入逻辑分离

### 任务 3.1 - 重构FloatingBubble组件
- **具体内容**：
  - 将FloatingBubble拆分为FloatingBubble.ts(UI)和FloatingBubbleInject.ts(逻辑)
  - FloatingBubble专注于UI渲染和样式
  - FloatingBubbleInject处理事件监听和业务逻辑
  - 建立两者间的清晰接口
- **预计时间**：6小时
- **验收标准**：UI与逻辑完全分离，接口清晰，功能完整
- _需求: 需求3_

### 任务 3.2 - 重构ArchiveButton组件
- **具体内容**：
  - 将ArchiveButton拆分为ArchiveButton.ts(UI)和ArchiveButtonInject.ts(逻辑)
  - 重构存档按钮的状态管理
  - 优化按钮的位置计算和显示逻辑
  - 通过事件总线处理存档请求
- **预计时间**：5小时
- **验收标准**：存档按钮功能完整，UI与逻辑分离清晰
- _需求: 需求3_

### 任务 3.3 - 重构HistoryBubble组件
- **具体内容**：
  - 将HistoryBubble拆分为HistoryBubble.ts(UI)和HistoryBubbleInject.ts(逻辑)
  - 优化历史记录的显示和交互
  - 添加搜索和过滤功能
  - 改进模态框的用户体验
- **预计时间**：6小时
- **验收标准**：历史气泡功能完整，用户体验良好
- _需求: 需求3_

## 阶段四：适配器模式优化

### 任务 4.1 - 重构BaseAIAdapter中介者架构
- **具体内容**：
  - 重新设计BaseAIAdapter作为中介者的职责
  - 实现依赖注入机制，管理所有子模块
  - 建立统一的初始化和销毁流程
  - 优化模板方法的设计
- **预计时间**：8小时
- **验收标准**：BaseAIAdapter作为中介者统一管理所有模块交互
- _需求: 需求1, 需求5_

### 任务 4.2 - 优化平台适配器实现
- **具体内容**：
  - 重构各平台适配器，简化子类实现
  - 统一选择器配置格式
  - 优化内容提取逻辑
  - 确保新增平台只需要最少的代码
- **预计时间**：6小时
- **验收标准**：平台适配器代码简洁，易于扩展
- _需求: 需求5_

### 任务 4.3 - 实现适配器工厂模式
- **具体内容**：
  - 创建AdapterFactory管理适配器创建
  - 实现平台检测和适配器选择逻辑
  - 添加适配器注册和发现机制
  - 支持动态加载新的适配器
- **预计时间**：4小时
- **验收标准**：适配器创建和管理机制完善，支持动态扩展
- _需求: 需求5_

## 阶段五：入口逻辑重构

### 任务 5.1 - 重构index.ts入口文件
- **具体内容**：
  - 简化index.ts的职责，专注于生命周期管理
  - 实现清晰的初始化流程
  - 添加错误处理和恢复机制
  - 优化SPA页面的重新初始化逻辑
- **预计时间**：4小时
- **验收标准**：入口文件职责清晰，初始化流程稳定可靠
- _需求: 需求7_

### 任务 5.2 - 优化Background通信
- **具体内容**：
  - 重构BackgroundHandler，提供统一的通信接口
  - 添加消息队列和重试机制
  - 优化消息序列化和错误处理
  - 建立通信状态监控
- **预计时间**：5小时
- **验收标准**：Background通信稳定可靠，具备完善的错误处理
- _需求: 需求1_


## 风险评估

### 高风险项
- BaseAIAdapter重构可能影响现有功能
- UI组件分离可能导致交互问题
- 事件总线重构可能影响性能

### 缓解措施
- 分阶段实施，每个阶段都要确保功能完整
- 保留现有代码作为备份
