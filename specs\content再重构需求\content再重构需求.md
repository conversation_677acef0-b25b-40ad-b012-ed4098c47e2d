# 需求文档

鉴于目前content的设计还是不太清晰，我要再次进行重构。

遵照如下设计规范：

1. 中介者模式 BaseAIAdapter.ts 统一的入口,所有模块通过 Adapter 交互，避免直接依赖
2. BaseAIAdapter与子类之间是 **模板方法（Template Method）+ 策略（Strategy）** 父子关系 ：新增站点只需新增子类，不改父类。
3. 事件总线，页面捕捉的各种事件，通过事件总线发送，其他模块通过注册获得通知
4. 每新增的自定义ui分为2个类，1个inject类，负责与content原来元素的交互，事件监听，响应，分发等，另外一个是ui类，存放在components目录，是样式和布局设计。这样来完成解耦。
5. 每个ts文件尽量不超过300行，超过了要拆分为多个文件
6. 不要过早进入性能优化

整体的目录结构如下：

```
index.ts content入口
/adapter
	/子类配置选择器
/shared  
	/BackgroundHandler 与background交互
	/EventBus 事件总线
	/EvenEnum 事件枚举
/capturer  页面捕捉模块
	/ContentCapturer.ts 网页捕捉模块 补充各种关心的页面组件，或组件内容
	/ContentListener  网页的各种事件的监听，注入到事件总线
/inject  页面注入模块
	/FloatingBubbleInject.ts 浮动小球的注册，管理ui的显示与隐藏，监听时间
	/HistoryBubbleInject.ts  历史模态页
	/ArchiveButtonInject.ts  存档按钮
/components
	/FloatingBubble.ts  
	/HistoryBubble.ts  
	/ArchiveButton.ts 
/types 定义类型
/utils 工具类
/configs 配置

```

