 # answer存储需求
 1. 在content页面，如果输入了提示词，点击存档时生成chat_uid存储数据库，这个逻辑不变
 2. 如果修改了提示词，再次点击存储，则生成新的chat_uid，
 3. 如果不存档点击了发送，则使用已存在的chat_uid, 存入数据库的`chat_prompt`和`chat_history`表，其中chat_history中is_answer字段为0
 4. 在answer回答完毕前，或者取消发送前，本content一直使用当前的chat_uid，在获得答案后，存入数据库的`chat_history`表，其中is_answer字段为1，platform_id为当前content的platform_id
 5. 当在其他content页面，弹出的提示词历史中，点击了提示词，即复制了当前提示词，注入当前页面的输入框，此时可以点击存档，但是chat_uid不变，即提示词内容相同，chat_uid不变，则不用更新chat_prompt表，在chat_history表插入记录，如果输入框内容手动改变了，则生成新的chat_uid，此时意味着是新的提示词了，可以重新存入数据库了
 6. 核心思想是，页面共享提示词，相同的提示词，chat_uid相同，在chat_prompt表只会有一条记录，但是在chat_history可以有多条记录，1个prompt可以在多个content也就是platform上回答。
 