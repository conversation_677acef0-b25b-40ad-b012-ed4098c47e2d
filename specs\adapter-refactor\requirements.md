# 需求文档

## 介绍

重构 BaseAIAdapter 和相关适配器子类的设计，使其成为 content 模块的核心应用上下文，提供更优雅的架构设计和更清晰的职责分离。

## 需求

### 需求 1 - 适配器架构重构

**用户故事：** 作为开发者，我希望有一个清晰的适配器架构，使得各个组件之间的依赖关系更加明确，代码更易维护。

#### 验收标准

1. When 初始化适配器时，BaseAIAdapter 应当包含平台的所有信息配置
2. When ArchiveButton 需要页面元素时，应当通过持有的 adapter 实例获得 inputContainer 等元素
3. When ArchiveButton 运行时，应当不包含任何从页面获得元素的逻辑
4. When BaseAIAdapter 运行时，应当负责所有页面元素的获得逻辑和事件监听逻辑

### 需求 2 - 事件总线设计

**用户故事：** 作为开发者，我希望使用事件总线模式来处理组件间通信，使得组件解耦更彻底。

#### 验收标准

1. When BaseAIAdapter 检测到页面事件时，应当通过事件总线发布事件
2. When ArchiveButton 需要监听事件时，应当在 ArchiveButton 中订阅相应事件
3. When 事件发生时，所有订阅者应当能够正确接收到事件通知

### 需求 3 - 选择器配置重构

**用户故事：** 作为开发者，我希望选择器配置更加灵活，通用选择器和平台特定选择器分离管理。

#### 验收标准

1. When 配置选择器时，platformConfigs.ts 应当只包含通用选择器配置
2. When 各平台适配器运行时，应当在 adapters 目录的子类中配置各自平台的选择器
3. When 获取选择器时，子类应当提供合并选择器的逻辑，返回合并后的选择器
4. When 父类获取页面元素时，应当使用合并后的选择器，不关心配置细节

### 需求 4 - 数据库交互规范

**用户故事：** 作为开发者，我希望 content 模块的数据库交互统一通过 Background 处理，确保架构清晰。

#### 验收标准

1. When ArchiveButton 需要数据库操作时，应当只能通过 MessagingService 传递给 Background
2. When content 中任何代码需要数据库操作时，应当只能通过 MessagingService 传递给 Background
3. When Background 接收到数据库操作请求时，应当由 Background 与数据库交互

### 需求 5 - InputManager 重构

**用户故事：** 作为开发者，我希望 InputManager 也通过 BaseAIAdapter 获取页面元素，保持架构一致性。

#### 验收标准

1. When InputManager 初始化时，应当持有 BaseAIAdapter 实例
2. When InputManager 需要页面元素时，应当通过 BaseAIAdapter 获得稳定的页面元素
3. When InputManager 运行时，应当不直接操作 DOM 查找元素

### 需求 6 - 应用上下文设计

**用户故事：** 作为开发者，我希望 BaseAIAdapter 成为 content 模块的核心应用上下文，统一管理组件间交互。

#### 验收标准

1. When content 模块初始化时，BaseAIAdapter 应当作为核心应用上下文
2. When 子组件需要与其他组件交流时，应当通过 BaseAIAdapter 获得交流机会
3. When 系统运行时，所有子组件应当寄托于 BaseAIAdapter
