# 适配器需求

BaseAIAdapter.t与adapters的适配器子类要有更优雅的设计。
尤其是与ArchiveButton的结合上，调整如下：
1. adapter要包含了平台的所有信息，现在的设计可以
2. ArchiveButton持有adapter实例，通过adapter实例获得想要的页面元素，比如inputContainer
3. ArchiveButton不关心也不包含从页面获得元素的逻辑
4. BaseAIAdapter抽象父类，负责所有页面元素的获得逻辑，事件的监听逻辑，可以采用事件总线的设计，ArchiveButton如果想监听这些事件，在ArchiveButton中订阅即可
5. 页面元素的选择器,修改为platformConfigs.ts配置不区分平台的通用选择器，在adapters目录的子类中配置各自平台的选择器，子类提供合并选择器的逻辑，返回合并后的选择器，在父类中完成获得页面元素。这样子类和只需要定期维护平台特有的选择器配置和正则配置，platformConfigs.ts维护通用的选择器配置。而父类不关系配置细节，只负责给其他业务模块，提供稳定的页面元素和事件通知。
6. ArchiveButton.ts与数据库从交互，只能通过MessagingService传递给Background，由Background与数据库交互。
7. content中所有的代码，都不能直接操作数据库，只能通过MessagingService传递给Background，由Background与数据库交互。
8. InputManager也要持有BaseAIAdapter,通过BaseAIAdapter获得稳定的页面元素
9. 设计中BaseAIAdapter为content的核心，相当于content模块的应用上下文，所有的子组件寄托与它，通过它获得与其他组件的交流机会。



