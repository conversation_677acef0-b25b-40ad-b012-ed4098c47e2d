# 实施计划

## 任务列表

### 1. 修改 ArchiveButton 类，优化提示词存储逻辑
- [ ] 1.1 修改 `archiveCurrentPrompt` 方法，在存储前检查是否存在相同提示词
  - 使用 `chatPromptService.findByPrompt` 查找现有提示词
  - 如果存在，复用其 chat_uid，不创建新记录
  - 如果不存在，生成新的 chat_uid 并创建新记录
  - _需求: 需求 2.2, 需求 3.2_

- [ ] 1.2 修改 `autoArchivePrompt` 方法，实现与 `archiveCurrentPrompt` 相同的逻辑
  - 确保自动存档和手动存档行为一致
  - _需求: 需求 1.3, 需求 2.1_

- [ ] 1.3 优化 `generateNewPromptId` 方法，确保生成的 ID 唯一
  - 使用时间戳和随机字符串组合生成唯一 ID
  - _需求: 需求 1.2_

### 2. 增强 AIAdapter 类，完善答案检测和存储功能
- [ ] 2.1 实现 `handleAnswerDetection` 方法，监听 AI 回答完成事件
  - 根据不同平台特性，实现答案检测逻辑
  - 使用 MutationObserver 或其他适当方法监听 DOM 变化
  - _需求: 需求 2.1_

- [ ] 2.2 实现 `storeAnswer` 方法，存储 AI 回答
  - 使用当前的 chat_uid 关联提示词和回答
  - 设置 is_answered = 1 标识为回答记录
  - _需求: 需求 2.1_

- [ ] 2.3 修改 `handlePromptSend` 方法，确保在发送提示词后正确设置当前 chat_uid
  - 在发送提示词后启动答案检测
  - _需求: 需求 2.2_

### 3. 更新 HistoryManager 类，实现跨平台提示词共享
- [ ] 3.1 修改 `handleHistoryItemClick` 方法，保持原有 chat_uid
  - 注入提示词到输入框时，保存原有 chat_uid
  - 设置 ArchiveButton 的当前 chat_uid
  - _需求: 需求 3.1, 需求 3.2_

- [ ] 3.2 优化 `showHistoryBubble` 方法，展示提示词和对应的 AI 回答
  - 按照对话顺序展示提示词和回答
  - 提供视觉区分，区分提示词和 AI 回答
  - _需求: 需求 3.3, 需求 3.4_

### 4. 优化数据库服务，确保数据一致性和性能
- [ ] 4.1 优化 `ChatPromptService.findByPrompt` 方法，提高查询性能
  - 添加适当的索引
  - 优化查询逻辑
  - _需求: 需求 4.1, 需求 4.4_

- [ ] 4.2 实现 `ChatHistoryService.createWithAnswer` 方法，存储 AI 回答
  - 确保正确设置 is_answered = 1
  - 关联到对应的提示词 chat_uid
  - _需求: 需求 2.1, 需求 4.2_

- [ ] 4.3 优化 `getUniqueChats` 方法，支持获取完整对话历史
  - 按照对话顺序返回提示词和回答
  - 支持分页和排序
  - _需求: 需求 3.3, 需求 4.3_

### 5. 测试和调试
- [ ] 5.1 编写单元测试，确保各组件功能正常
  - 测试提示词存储和复用逻辑
  - 测试答案检测和存储功能
  - 测试跨平台提示词共享
  - _需求: 需求 4.4_

- [ ] 5.2 进行集成测试，验证整体功能
  - 测试在不同 AI 平台上的表现
  - 测试大量历史记录下的性能
  - _需求: 需求 4.4_

- [ ] 5.3 进行性能优化，确保系统在处理大量历史记录时保持高效
  - 优化数据库查询
  - 优化 DOM 操作
  - _需求: 需求 4.1, 需求 4.2, 需求 4.3, 需求 4.4_

## 优先级和依赖关系

### 高优先级任务
1. 任务 1.1, 1.2, 1.3：修改 ArchiveButton 类（基础功能）
2. 任务 2.1, 2.2, 2.3：增强 AIAdapter 类（核心功能）

### 中优先级任务
1. 任务 3.1, 3.2：更新 HistoryManager 类（用户体验）
2. 任务 4.1, 4.2：优化数据库服务（性能和稳定性）

### 低优先级任务
1. 任务 4.3：优化 getUniqueChats 方法（高级功能）
2. 任务 5.1, 5.2, 5.3：测试和调试（质量保证）

## 实施顺序
1. 先完成高优先级任务，确保基础功能和核心功能正常工作
2. 再完成中优先级任务，提升用户体验和系统性能
3. 最后完成低优先级任务，完善高级功能和质量保证