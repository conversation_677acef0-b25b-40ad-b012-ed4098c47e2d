import { BaseAIAdapter } from './BaseAIAdapter'
import { Conversation, Message } from '@/types'
import { ClaudeConfig } from '../configs/Consts'
import { SelectorConfig } from '../capturer/ElementManager'

export class ClaudeAdapter extends BaseAIAdapter {
  constructor() {
    super(ClaudeConfig)
  }

  /**
   * 获取 Claude 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        'div[contenteditable="true"]'
      ],
      sendButton: [
        'button[aria-label*="Send"]',
        'button[type="submit"]'
      ],
      messageContainer: [
        '.font-claude-message',
        '[data-is-streaming]',
        '.message'
      ],
      inputContainer: [
        '.composer-parent',
        '.input-container',
        '.editor-container'
      ]
    }
  }





  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = this.findElements(this.config.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        // 使用配置的用户消息识别
        const isUser = element.closest('[data-is-streaming="false"]')?.querySelector('[data-testid="user-message"]') !== null ||
                      element.classList.contains('user-message') ||
                      element.getAttribute('data-message-role') === 'user'

        const contentElement = element.querySelector('.font-claude-message') ||
                              element.querySelector('[data-testid="message-content"]') ||
                              element

        if (contentElement) {
          const content = this.cleanContent(contentElement.textContent || '')
          if (content && content.length > 0) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 使用配置的标题选择器
      const titleElement = this.findElement(this.config.selectors.conversationTitle || ['h1'])
      const title = titleElement.element?.textContent || `Claude对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `claude-${Date.now()}`,
        platform: 'claude',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract Claude conversation error:', error)
      return null
    }
  }

  /**
   * 提取最新的AI回答
   */
  protected async extractLatestAnswer(): Promise<string | null> {
    try {
      // 使用配置的助手消息选择器
      const answerElements = this.findElements([
        '.font-claude-message',
        '[data-testid="message-content"]',
        ...(this.config.selectors.assistantMessage || [])
      ])

      if (answerElements.length === 0) return null

      // 获取最后一个答案（排除用户消息）
      for (let i = answerElements.length - 1; i >= 0; i--) {
        const element = answerElements[i]
        const isUserMessage = element.closest('[data-testid="user-message"]') !== null

        if (!isUserMessage) {
          const content = this.cleanContent(element.textContent || '')
          if (content.length > 0) {
            console.log('【EchoSync】Claude extracted answer:', content.substring(0, 100) + '...')
            return content
          }
        }
      }

      return null
    } catch (error) {
      console.error('【EchoSync】Extract Claude latest answer error:', error)
      return null
    }
  }
}
