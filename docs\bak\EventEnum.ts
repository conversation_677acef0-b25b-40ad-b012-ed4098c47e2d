/**
 * Content模块事件枚举定义
 * 统一管理所有事件类型，确保类型安全
 */
export enum ContentEvent {
  // 输入相关事件
  INPUT_FOCUSED = 'input-focused',
  INPUT_CHANGED = 'input-changed',
  INPUT_BLURRED = 'input-blurred',
  
  // 页面相关事件
  PAGE_CHANGED = 'page-changed',
  PAGE_LOADED = 'page-loaded',
  
  // 消息相关事件
  MESSAGE_SENT = 'message-sent',
  MESSAGE_RECEIVED = 'message-received',
  
  // UI组件事件
  ARCHIVE_BUTTON_CLICKED = 'archive-button-clicked',
  FLOATING_BUBBLE_CLICKED = 'floating-bubble-clicked',
  HISTORY_BUBBLE_OPENED = 'history-bubble-opened',
  HISTORY_BUBBLE_CLOSED = 'history-bubble-closed',
  
  // 内容捕获事件
  CONTENT_CAPTURED = 'content-captured',
  CONVERSATION_UPDATED = 'conversation-updated',
  ELEMENT_FOUND = 'element-found',
  ELEMENT_LOST = 'element-lost',
  
  // 存档相关事件
  ARCHIVE_REQUESTED = 'archive-requested',
  ARCHIVE_COMPLETED = 'archive-completed',
  ARCHIVE_FAILED = 'archive-failed',
  
  // 系统事件
  ADAPTER_READY = 'adapter-ready',
  COMPONENT_DESTROYED = 'component-destroyed',
  INITIALIZATION_COMPLETE = 'initialization-complete'
}

/**
 * 事件数据类型映射
 * 为每个事件定义对应的数据结构
 */
export interface EventDataMap {
  [ContentEvent.INPUT_FOCUSED]: { element: HTMLElement; value: string }
  [ContentEvent.INPUT_CHANGED]: { element?: HTMLElement; value: string }
  [ContentEvent.INPUT_BLURRED]: { element: HTMLElement; value: string }
  
  [ContentEvent.PAGE_CHANGED]: { url: string; platform?: string }
  [ContentEvent.PAGE_LOADED]: { url: string; platform?: string }
  
  [ContentEvent.MESSAGE_SENT]: { content: string; platform?: string }
  [ContentEvent.MESSAGE_RECEIVED]: { content: string; platform?: string }
  
  [ContentEvent.ARCHIVE_BUTTON_CLICKED]: { promptId: string }
  [ContentEvent.FLOATING_BUBBLE_CLICKED]: { position: { x: number; y: number } }
  [ContentEvent.HISTORY_BUBBLE_OPENED]: { trigger: string }
  [ContentEvent.HISTORY_BUBBLE_CLOSED]: { reason: string }
  
  [ContentEvent.CONTENT_CAPTURED]: { content: string; type: string }
  [ContentEvent.CONVERSATION_UPDATED]: { conversation: any }
  [ContentEvent.ELEMENT_FOUND]: { type: string; element: HTMLElement }
  [ContentEvent.ELEMENT_LOST]: { type: string }
  
  [ContentEvent.ARCHIVE_REQUESTED]: { content: string; promptId: string }
  [ContentEvent.ARCHIVE_COMPLETED]: { promptId: string; success: boolean }
  [ContentEvent.ARCHIVE_FAILED]: { promptId: string; error: string }
  
  [ContentEvent.ADAPTER_READY]: { platform: string }
  [ContentEvent.COMPONENT_DESTROYED]: { component: string }
  [ContentEvent.INITIALIZATION_COMPLETE]: { platform: string; timestamp: number }
}