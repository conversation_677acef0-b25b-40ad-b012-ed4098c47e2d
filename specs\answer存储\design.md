# 技术方案设计

## 1. 系统架构

当前 EchoSync 插件采用了典型的 Chrome 扩展架构，包含以下主要组件：

- **Background Service Worker**：负责数据库操作和后台任务
- **Content Scripts**：注入到各 AI 平台页面，处理用户交互和提示词管理
- **Popup & Options Pages**：提供用户界面和配置选项

数据存储使用 IndexedDB（通过 Dexie.js 库），主要包含以下表：
- `chatPrompt`：存储提示词内容和唯一标识（chat_uid）
- `chatHistory`：存储提示词使用记录和 AI 回答，通过 chat_uid 关联到 chatPrompt
- `platform`：存储支持的 AI 平台信息

## 2. 数据结构设计

### 现有数据结构

```typescript
// 聊天提示词实体
export interface ChatPrompt {
  id: number
  chat_prompt: string
  chat_uid: string
  create_time: number
  is_synced: number
  is_delete: number
}

// 聊天历史实体
export interface ChatHistory {
  id: number
  chat_answer?: string
  chat_uid: string
  platform_id: number
  tags?: string
  chat_group_name?: string
  chat_sort?: number
  p_uid?: string
  create_time: number
  is_synced: number
  is_answered: number
  is_delete: number
}
```

### 数据关系

- 一个提示词（`ChatPrompt`）可以有多个历史记录（`ChatHistory`）
- 通过 `chat_uid` 字段关联 `ChatPrompt` 和 `ChatHistory`
- `ChatHistory` 中的 `is_answered` 字段表示该记录是提示词（0）还是回答（1）

## 3. 技术实现方案

### 3.1 提示词存储与 chat_uid 生成

当前系统在 `ArchiveButton` 类中已经实现了提示词存储功能，但需要优化以支持跨平台共享。主要修改点：

1. 修改 `archiveCurrentPrompt` 和 `autoArchivePrompt` 方法，在存储前先检查是否存在相同提示词
2. 如果存在相同提示词，复用其 chat_uid，不创建新记录
3. 如果提示词内容变化，则生成新的 chat_uid

```typescript
// 伪代码示例
async archiveCurrentPrompt(promptContent) {
  // 检查是否存在相同提示词
  const existingPrompt = await chatPromptService.findByPrompt(promptContent);
  
  if (existingPrompt.success && existingPrompt.data) {
    // 复用现有提示词的 chat_uid
    this.currentPromptId = existingPrompt.data.chat_uid;
  } else {
    // 生成新的提示词 ID
    this.generateNewPromptId();
    // 创建新的提示词记录
    await chatPromptService.create({
      chat_prompt: promptContent,
      chat_uid: this.currentPromptId
    });
  }
  
  // 创建历史记录
  await chatHistoryService.create({
    chat_uid: this.currentPromptId,
    platform_id: currentPlatform.id,
    is_answered: 0
  });
}
```

### 3.2 答案存储机制

需要在 `AIAdapter` 类中增强答案检测和存储功能：

1. 监听 AI 回答完成事件
2. 提取回答内容
3. 使用当前的 chat_uid 存储回答

```typescript
// 伪代码示例
async storeAnswer(answerContent) {
  if (!this.currentPromptId) return;
  
  await chatHistoryService.create({
    chat_answer: answerContent,
    chat_uid: this.currentPromptId,
    platform_id: this.currentPlatform.id,
    is_answered: 1
  });
}
```

### 3.3 跨平台提示词共享

在 `HistoryManager` 类中，当用户点击历史记录中的提示词时：

1. 注入提示词到当前输入框
2. 保持原有的 chat_uid
3. 只有当用户修改提示词内容时才生成新的 chat_uid

```typescript
// 伪代码示例
handleHistoryItemClick(chat) {
  // 注入提示词到输入框
  this.inputManager.injectPrompt(chat.chat_prompt);
  
  // 设置当前提示词 ID 为历史记录中的 ID
  this.archiveButton.setCurrentPromptId(chat.chat_uid);
  
  // 标记为已存档状态
  this.archiveButton.markAsArchived();
}
```

### 3.4 数据库服务优化

需要优化 `ChatPromptService` 和 `ChatHistoryService` 以支持提示词共享：

1. 增强 `findByPrompt` 方法的性能
2. 优化 `createWithPrompt` 方法，确保相同提示词只存储一次
3. 确保 `chat_history` 表中正确设置 `is_answered` 字段

## 4. 实现步骤

1. 修改 `ArchiveButton` 类，优化提示词存储逻辑
2. 增强 `AIAdapter` 类，完善答案检测和存储功能
3. 更新 `HistoryManager` 类，实现跨平台提示词共享
4. 优化数据库服务，确保数据一致性和性能
5. 添加必要的日志和错误处理

## 5. 兼容性考虑

本方案不需要修改数据库结构，可以直接在现有架构上实现，确保与已存储的数据兼容。对于已有的提示词和回答，系统将继续正常工作，新的共享机制只会应用于新创建的提示词。