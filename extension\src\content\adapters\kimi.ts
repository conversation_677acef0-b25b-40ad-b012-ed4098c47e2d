import { BaseAIAdapter } from './BaseAIAdapter'
import { Conversation, Message } from '@/types'
import { KimiConfig } from '../configs/Consts'
import { SelectorConfig } from '../capturer/ElementManager'

export class KimiAdapter extends BaseAIAdapter {
  constructor() {
    console.log('【EchoSync】KimiAdapter constructor called')
    super(KimiConfig)
    console.log('【EchoSync】KimiAdapter initialized with config:', KimiConfig)

    // 延迟检测并优化选择器
    setTimeout(() => {
      this.optimizeSelectors()
    }, 2000)
  }

  /**
   * 获取 Kimi 平台特定的选择器配置
   */
  getSelectors(): SelectorConfig {
    return {
      inputField: [
        '[data-lexical-editor="true"]',
        '.chat-input-editor[contenteditable="true"]',
        '.chat-input-editor',
        'textarea[placeholder*="请输入"]',
        'textarea[placeholder*="输入"]',
        'textarea[placeholder*="message"]'
      ],
      sendButton: [
        '.send-button-container:not(.disabled) .send-button',
        '.send-button:not(.disabled)',
        '.chat-editor-action .send-button-container:not(.disabled) .send-button',
        'button[aria-label*="发送"]',
        'button[aria-label*="Send"]',
        '.send-btn',
        '[data-testid*="send"]'
      ],
      messageContainer: [
        '.chat-content-item',
        '.segment',
        '.message-item',
        '.conversation-item',
        '.message-wrapper',
        '.chat-message'
      ],
      inputContainer: [
        '.chat-input-container',
        '.chat-editor',
        '.input-wrapper',
        '.editor-container'
      ]
    }
  }



  /**
   * 动态优化选择器（重写为使用配置驱动）
   */
  private optimizeSelectors(): void {
    console.log('【EchoSync】KimiAdapter optimizing selectors...')

    // 检测输入框
    const inputResult = this.findElement(this.config.selectors.inputField)
    if (inputResult.element) {
      console.log('【EchoSync】Found input element with selector:', inputResult.selector)
    } else {
      console.warn('【EchoSync】No input element found with any configured selector')
    }

    // 检测发送按钮
    const buttonResult = this.findElement(this.config.selectors.sendButton)
    if (buttonResult.element) {
      console.log('【EchoSync】Found send button with selector:', buttonResult.selector)
    } else {
      console.warn('【EchoSync】No send button found with any configured selector')
    }

    console.log('【EchoSync】Selectors optimization complete')
  }



  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = this.findElements(this.config.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []

      messageElements.forEach((element, index) => {
        // Kimi使用特定的类名来区分用户和助手消息
        const isUser = element.classList.contains('chat-content-item-user') ||
                      element.classList.contains('segment-user') ||
                      element.querySelector('.user-content') !== null

        const isAssistant = element.classList.contains('chat-content-item-assistant') ||
                           element.classList.contains('segment-assistant') ||
                           element.querySelector('.markdown-container') !== null

        // 只处理用户或助手消息
        if (!isUser && !isAssistant) return

        const contentElement = element.querySelector('.user-content') ||
                              element.querySelector('.markdown-container .markdown') ||
                              element.querySelector('.segment-content-box') ||
                              element

        if (contentElement) {
          const content = this.cleanContent(contentElement.textContent || '')
          if (content) {
            messages.push({
              id: `msg-${index}`,
              role: isUser ? 'user' : 'assistant',
              content,
              timestamp: Date.now() - (messageElements.length - index) * 1000
            })
          }
        }
      })

      if (messages.length === 0) return null

      // 使用配置的标题选择器
      const titleElement = this.findElement(this.config.selectors.conversationTitle || ['.chat-header-content h2', '.chat-name'])
      const title = titleElement.element?.textContent?.trim() || `Kimi对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `kimi-${Date.now()}`,
        platform: 'kimi',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract Kimi conversation error:', error)
      return null
    }
  }

  /**
   * 重写自定义输入获取方法，适配Kimi的Lexical编辑器
   */
  protected getCustomCurrentInput(): string | null {
    const inputResult = this.findElement(this.config.selectors.inputField)
    if (!inputResult.element) {
      console.log('【EchoSync】Kimi getCustomCurrentInput: No input element found')
      return null
    }

    const inputElement = inputResult.element as HTMLElement

    // Kimi使用Lexical编辑器，需要特殊处理
    if (inputElement.hasAttribute('data-lexical-editor')) {
      // 获取纯文本内容，排除<br>标签
      const textContent = inputElement.textContent || ''
      const content = textContent.trim()
      console.log('【EchoSync】Kimi getCustomCurrentInput (Lexical):', content)
      return content
    }

    // 对于其他类型的输入元素，返回null使用默认实现
    return null
  }

  /**
   * 提取最新的AI回答
   */
  protected async extractLatestAnswer(): Promise<string | null> {
    try {
      // 查找最新的AI回答内容
      const answerElements = this.findElements([
        '.message-content',
        '.content',
        '.segment-content',
        '.markdown-container .markdown',
        ...(this.config.selectors.assistantMessage || [])
      ])

      if (answerElements.length === 0) return null

      // 获取最后一个答案（排除用户消息）
      for (let i = answerElements.length - 1; i >= 0; i--) {
        const element = answerElements[i]
        const isUserMessage = element.closest('[data-role="user"]') !== null ||
                             element.closest('.user-message') !== null ||
                             element.closest('.chat-content-item-user') !== null

        if (!isUserMessage) {
          const content = this.cleanContent(element.textContent || '')
          if (content.length > 0) {
            console.log('【EchoSync】Kimi extracted answer:', content.substring(0, 100) + '...')
            return content
          }
        }
      }

      return null
    } catch (error) {
      console.error('【EchoSync】Extract Kimi latest answer error:', error)
      return null
    }
  }
}
