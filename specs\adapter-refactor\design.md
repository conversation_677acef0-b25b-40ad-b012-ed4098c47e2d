# 技术方案设计

## 架构概述

重构 BaseAIAdapter 为 content 模块的核心应用上下文，采用事件总线模式实现组件间解耦，通过依赖注入和配置分离实现更优雅的架构设计。

## 技术栈

- **语言**: TypeScript
- **设计模式**: 
  - 观察者模式（事件总线）
  - 依赖注入
  - 策略模式（适配器模式）
  - 单例模式（应用上下文）
- **架构模式**: 分层架构 + 事件驱动架构

## 核心组件设计

### 1. 事件总线系统

```mermaid
graph TD
    A[EventBus] --> B[BaseAIAdapter]
    B --> C[ArchiveButton]
    B --> D[InputManager]
    B --> E[FloatingBubble]
    
    C --> |subscribe| A
    D --> |subscribe| A
    E --> |subscribe| A
    
    B --> |publish| A
```

#### EventBus 接口设计
```typescript
interface EventBus {
  subscribe<T>(event: string, callback: (data: T) => void): void
  unsubscribe(event: string, callback: Function): void
  publish<T>(event: string, data: T): void
}
```

### 2. 选择器配置系统

```mermaid
graph LR
    A[platformConfigs.ts<br/>通用选择器] --> C[BaseAIAdapter]
    B[子类适配器<br/>平台特定选择器] --> C
    C --> D[合并选择器逻辑]
    D --> E[DOM元素获取]
```

#### 配置接口设计
```typescript
interface SelectorConfig {
  inputField: string[]
  sendButton: string[]
  messageContainer: string[]
}

interface PlatformAdapter {
  getSelectors(): SelectorConfig
  mergeSelectors(common: SelectorConfig): SelectorConfig
}
```

### 3. 应用上下文架构

```mermaid
graph TD
    A[BaseAIAdapter<br/>应用上下文] --> B[EventBus<br/>事件总线]
    A --> C[ElementManager<br/>元素管理器]
    A --> D[MessagingService<br/>消息服务]
    
    E[ArchiveButton] --> A
    F[InputManager] --> A
    G[FloatingBubble] --> A
    
    A --> H[Background<br/>数据库操作]
```

## 数据流设计

### 1. 元素获取流程
```
子组件请求 → BaseAIAdapter → 合并选择器 → DOM查询 → 缓存元素 → 返回元素
```

### 2. 事件通信流程
```
页面事件 → BaseAIAdapter → EventBus.publish → 订阅组件接收 → 组件响应
```

### 3. 数据库操作流程
```
组件请求 → MessagingService → Background → 数据库 → 结果返回 → 组件处理
```

## 接口设计

### BaseAIAdapter 核心接口
```typescript
abstract class BaseAIAdapter {
  protected eventBus: EventBus
  protected elementManager: ElementManager
  protected messagingService: MessagingService
  
  // 抽象方法，子类实现
  abstract getSelectors(): SelectorConfig
  
  // 公共方法
  getElement(type: ElementType): HTMLElement | null
  addEventListener(event: string, callback: Function): void
  removeEventListener(event: string, callback: Function): void
  sendMessage(action: string, data: any): Promise<any>
}
```

### 组件基类接口
```typescript
abstract class BaseComponent {
  protected adapter: BaseAIAdapter
  
  constructor(adapter: BaseAIAdapter) {
    this.adapter = adapter
  }
  
  abstract initialize(): void
  abstract destroy(): void
}
```

## 实现策略

### 阶段1：事件总线实现
1. 创建 EventBus 类
2. 在 BaseAIAdapter 中集成事件总线
3. 重构现有事件监听逻辑

### 阶段2：选择器配置重构
1. 分离通用选择器到 platformConfigs.ts
2. 在各适配器子类中添加平台特定选择器
3. 实现选择器合并逻辑

### 阶段3：组件重构
1. 重构 ArchiveButton 使其依赖 BaseAIAdapter
2. 重构 InputManager 使其依赖 BaseAIAdapter
3. 移除组件中的直接 DOM 操作

### 阶段4：数据库交互规范化
1. 确保所有数据库操作通过 MessagingService
2. 移除 content 中的直接数据库访问代码

## 性能考虑

1. **元素缓存**: 缓存常用 DOM 元素，避免重复查询
2. **事件防抖**: 对高频事件进行防抖处理
3. **懒加载**: 按需初始化组件和监听器
4. **内存管理**: 提供清理机制，避免内存泄漏
