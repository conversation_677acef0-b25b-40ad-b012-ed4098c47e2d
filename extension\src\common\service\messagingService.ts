import type { ChromeMessage, MessageType } from '@/types'

export class MessagingService {
  private static readonly SW_PING_TIMEOUT = 5000 // 5秒超时
  private static readonly SW_WAKE_DELAY = 100 // 唤醒后等待时间

  // 发送消息到background script
  static async   sendToBackground<T = any>(
    type: MessageType,
    payload?: any,
    retries: number = 2
  ): Promise<T> {
    const message: ChromeMessage = {
      type,
      payload,
      timestamp: Date.now()
    }

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        // 检查扩展上下文是否有效
        if (!chrome.runtime?.id) {
          throw new Error('Extension context invalidated')
        }

        // 确保Service Worker处于活跃状态
        await this.ensureServiceWorkerReady()

        const response = await chrome.runtime.sendMessage(message)
        return response
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)

        // 如果是Service Worker相关错误且还有重试次数
        if ((errorMessage.includes('Extension context invalidated') ||
             errorMessage.includes('Could not establish connection') ||
             errorMessage.includes('Receiving end does not exist')) &&
            attempt < retries) {
          console.warn(`【EchoSync】Service Worker connection issue, retrying... (${attempt + 1}/${retries + 1})`)

          // 尝试唤醒Service Worker
          await this.wakeUpServiceWorker()

          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, this.SW_WAKE_DELAY * (attempt + 1)))
          continue
        }

        // 如果是其他错误或重试次数用完，抛出错误
        console.error('【EchoSync】Send to background error:', error)
        throw error
      }
    }

    throw new Error('Failed to send message after retries')
  }

  // 发送消息到content script
  static async sendToContentScript<T = any>(
    tabId: number,
    type: MessageType,
    payload?: any
  ): Promise<T> {
    const message: ChromeMessage = {
      type,
      payload,
      timestamp: Date.now(),
      tabId
    }

    try {
      const response = await chrome.tabs.sendMessage(tabId, message)
      return response
    } catch (error) {
      console.error('【EchoSync】Send to content script error:', error)
      throw error
    }
  }

  // 发送消息到所有匹配的tabs
  static async sendToAllTabs<T = any>(
    type: MessageType,
    payload?: any,
    urlPattern?: string
  ): Promise<T[]> {
    try {
      const tabs = await chrome.tabs.query({
        url: urlPattern || '*://*/*'
      })

      const promises = tabs.map(tab => {
        if (tab.id) {
          return this.sendToContentScript(tab.id, type, payload).catch(() => null)
        }
        return null
      })

      const results = await Promise.all(promises)
      return results.filter(result => result !== null)
    } catch (error) {
      console.error('【EchoSync】Send to all tabs error:', error)
      return []
    }
  }

  // 监听消息
  static onMessage<T = any>(
    callback: (
      message: ChromeMessage<T>,
      sender: chrome.runtime.MessageSender,
      sendResponse: (response?: any) => void
    ) => void | Promise<void>
  ): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      const result = callback(message, sender, sendResponse)
      
      // 如果返回Promise，处理异步响应
      if (result instanceof Promise) {
        result
          .then(response => sendResponse(response))
          .catch(error => sendResponse({ error: error.message }))
        return true // 保持消息通道开放
      }
    })
  }

  // 获取当前活动tab
  static async getCurrentTab(): Promise<chrome.tabs.Tab | null> {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true
      })
      return tab || null
    } catch (error) {
      console.error('【EchoSync】Get current tab error:', error)
      return null
    }
  }

  // 检查tab是否支持content script
  static async isTabSupported(tabId: number): Promise<boolean> {
    try {
      const tab = await chrome.tabs.get(tabId)
      if (!tab.url) return false

      const supportedDomains = [
        'chat.openai.com',
        'chat.deepseek.com',
        'claude.ai',
        'gemini.google.com',
        'poe.com',
        'perplexity.ai',
        'you.com'
      ]

      return supportedDomains.some(domain => tab.url!.includes(domain))
    } catch (error) {
      console.error('【EchoSync】Check tab supported error:', error)
      return false
    }
  }

  /**
   * 检查Service Worker是否存活
   */
  static async checkServiceWorkerAlive(): Promise<boolean> {
    try {
      // 检查扩展上下文是否有效
      if (!chrome.runtime?.id) {
        return false
      }

      // 尝试发送ping消息来检查SW状态
      const pingPromise = chrome.runtime.sendMessage({ type: 'SW_PING', timestamp: Date.now() })
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('SW ping timeout')), this.SW_PING_TIMEOUT)
      )

      await Promise.race([pingPromise, timeoutPromise])
      return true
    } catch (error) {
      console.warn('【EchoSync】Service Worker ping failed:', error)
      return false
    }
  }

  /**
   * 唤醒Service Worker
   */
  static async wakeUpServiceWorker(): Promise<void> {
    try {
      console.log('【EchoSync】Attempting to wake up Service Worker...')

      // 尝试多种方式唤醒SW
      const wakeUpMethods = [
        // 方法1: 发送简单消息
        () => chrome.runtime.sendMessage({ type: 'SW_WAKE_UP', timestamp: Date.now() }),

        // 方法2: 获取平台信息（轻量级API调用）
        () => chrome.runtime.getPlatformInfo(),

        // 方法3: 查询存储（如果其他方法失败）
        () => chrome.storage.local.get('sw_wake_up')
      ]

      for (const method of wakeUpMethods) {
        try {
          await method()
          console.log('【EchoSync】Service Worker wake-up successful')
          return
        } catch (error) {
          console.warn('【EchoSync】Wake-up method failed, trying next...', error)
        }
      }

      console.warn('【EchoSync】All wake-up methods failed')
    } catch (error) {
      console.error('【EchoSync】Service Worker wake-up error:', error)
    }
  }

  /**
   * 确保Service Worker准备就绪
   */
  static async ensureServiceWorkerReady(): Promise<void> {
    try {
      const isAlive = await this.checkServiceWorkerAlive()

      if (!isAlive) {
        console.log('【EchoSync】Service Worker not responsive, attempting to wake up...')
        await this.wakeUpServiceWorker()

        // 等待SW启动
        await new Promise(resolve => setTimeout(resolve, this.SW_WAKE_DELAY))

        // 再次检查状态
        const isAliveAfterWakeUp = await this.checkServiceWorkerAlive()
        if (!isAliveAfterWakeUp) {
          console.warn('【EchoSync】Service Worker still not responsive after wake-up attempt')
        }
      }
    } catch (error) {
      console.error('【EchoSync】Error ensuring Service Worker readiness:', error)
    }
  }
}
