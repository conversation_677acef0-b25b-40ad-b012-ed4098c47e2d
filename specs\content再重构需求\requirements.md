# Content模块重构需求文档

## 介绍

基于当前Chrome插件项目的content模块现状分析，需要进行深度重构以提升代码架构的清晰度、可维护性和扩展性。当前模块虽然已经实现了基本的适配器模式和事件总线机制，但在职责分离、组件解耦和代码组织方面仍有优化空间。

### 当前架构问题分析

1. **职责混乱**：BaseAIAdapter承担了过多职责，包括UI管理、事件处理、元素查找等
2. **组件耦合**：UI组件与业务逻辑耦合度较高，难以独立测试和维护  
3. **目录结构不清晰**：缺乏明确的分层架构，inject和components概念混淆
4. **事件处理分散**：页面事件监听逻辑分散在各个组件中，缺乏统一管理
5. **文件过大**：部分文件超过300行，违反单一职责原则

### 重构设计原则

1. **中介者模式**：BaseAIAdapter作为统一入口，所有模块通过Adapter交互，避免直接依赖
2. **模板方法+策略模式**：BaseAIAdapter与子类采用模板方法+策略模式，新增站点只需新增子类，不改父类
3. **事件总线**：页面捕获的各种事件通过事件总线发送，其他模块通过注册获得通知
4. **UI与逻辑分离**：每个自定义UI分为inject类（交互逻辑）和component类（样式布局），实现完全解耦
5. **文件大小控制**：每个ts文件尽量不超过300行，超过了要拆分为多个文件
6. **避免过早优化**：专注于架构清晰度，不要过早进入性能优化

### 目标架构

```
src/content/
├── index.ts                    # Content入口，管理适配器生命周期
├── adapters/                   # 平台适配器
│   ├── BaseAIAdapter.ts       # 基础适配器（中介者）
│   ├── chatgpt.ts            # ChatGPT适配器
│   ├── claude.ts             # Claude适配器
│   └── ...                   # 其他平台适配器
├── shared/                    # 共享模块
│   ├── BackgroundHandler.ts  # Background通信处理
│   ├── EventBus.ts           # 事件总线
│   └── EventEnum.ts          # 事件枚举定义
├── capturer/                  # 页面捕获模块
│   ├── ContentCapturer.ts    # 网页内容捕获
│   └── ContentListener.ts    # 页面事件监听
├── inject/                    # 页面注入模块
│   ├── FloatingBubbleInject.ts  # 浮动气泡注入逻辑
│   ├── HistoryBubbleInject.ts   # 历史气泡注入逻辑
│   └── ArchiveButtonInject.ts   # 存档按钮注入逻辑
├── components/                # UI组件
│   ├── FloatingBubble.ts     # 浮动气泡UI
│   ├── HistoryBubble.ts      # 历史气泡UI
│   └── ArchiveButton.ts      # 存档按钮UI
├── types/                     # 类型定义
├── utils/                     # 工具类
└── configs/                   # 配置文件
```

## 需求

### 需求 1 - 重构BaseAIAdapter中介者架构

**用户故事：** 作为开发者，我希望BaseAIAdapter作为中介者统一管理所有模块交互，避免模块间直接依赖，以便提高代码的可维护性。

#### 验收标准

1. When 重构BaseAIAdapter时，系统应当将其定位为中介者，统一管理所有模块的交互
2. When 其他模块需要通信时，系统应当通过BaseAIAdapter进行中转，避免直接依赖
3. When 管理组件生命周期时，系统应当由BaseAIAdapter统一控制初始化、运行和销毁

### 需求 2 - 实现事件总线统一管理

**用户故事：** 作为开发者，我希望所有页面事件通过事件总线统一管理，其他模块通过注册监听获得通知，以便实现松耦合的事件处理机制。

#### 验收标准

1. When 页面发生事件时，系统应当通过事件总线发布事件通知
2. When 模块需要监听事件时，系统应当通过事件总线注册监听器
3. When 定义事件类型时，系统应当在EventEnum中统一管理所有事件枚举

### 需求 3 - 分离UI组件与注入逻辑

**用户故事：** 作为开发者，我希望每个UI功能分为inject类和component类，inject负责交互逻辑，component负责样式布局，以便实现UI与逻辑的完全解耦。

#### 验收标准

1. When 实现UI功能时，系统应当创建对应的inject类处理交互逻辑
2. When 设计UI样式时，系统应当创建对应的component类处理样式和布局
3. When inject类与component类交互时，系统应当通过明确的接口进行通信

### 需求 4 - 建立页面捕获模块

**用户故事：** 作为开发者，我希望有专门的capturer模块负责页面内容捕获和事件监听，以便统一管理页面交互逻辑。

#### 验收标准

1. When 捕获页面内容时，系统应当通过ContentCapturer统一处理
2. When 监听页面事件时，系统应当通过ContentListener统一管理
3. When 捕获到内容或事件时，系统应当通过事件总线通知相关模块

### 需求 5 - 优化适配器模式实现

**用户故事：** 作为开发者，我希望BaseAIAdapter与子类采用标准的模板方法+策略模式，新增平台只需新增子类，不修改父类，以便提高扩展性。

#### 验收标准

1. When 设计BaseAIAdapter时，系统应当提供完整的模板方法流程
2. When 实现子类适配器时，系统应当只需要实现平台特定的策略方法
3. When 新增平台支持时，系统应当只需要创建新的子类适配器

### 需求 6 - 控制文件大小和职责

**用户故事：** 作为开发者，我希望每个文件都遵循单一职责原则，文件大小控制在300行以内，以便提高代码的可读性和可维护性。

#### 验收标准

1. When 文件超过300行时，系统应当按照职责拆分为多个文件
2. When 设计类时，系统应当确保每个类只承担单一职责
3. When 组织代码时，系统应当按照功能模块进行清晰的目录划分

### 需求 7 - 重构入口管理逻辑

**用户故事：** 作为开发者，我希望index.ts作为清晰的入口文件，专注于适配器生命周期管理，以便简化初始化流程。

#### 验收标准

1. When 初始化content模块时，系统应当通过index.ts统一管理适配器生命周期
2. When 检测平台时，系统应当有清晰的平台识别和适配器创建流程
3. When 处理页面变化时，系统应当能够正确地重新初始化适配器

### 需求 8 - 建立统一的类型系统

**用户故事：** 作为开发者，我希望有统一的类型定义系统，所有模块共享类型定义，以便提高类型安全性。

#### 验收标准

1. When 定义数据结构时，系统应当在types目录中统一管理
2. When 模块间通信时，系统应当使用统一的类型定义
3. When 扩展功能时，系统应当能够复用现有的类型定义
